{"name": "supplyline-mro-suite", "private": true, "version": "3.5.3", "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:e2e:report": "playwright show-report", "test:e2e:install": "playwright install", "test:e2e:auth": "playwright test tests/e2e/auth/", "test:e2e:tools": "playwright test tests/e2e/tools/", "test:e2e:chemicals": "playwright test tests/e2e/chemicals/", "test:e2e:dashboard": "playwright test tests/e2e/dashboard/", "test:e2e:mobile": "playwright test --project='Mobile Chrome'", "test:e2e:performance": "playwright test --project=performance", "test:e2e:visual": "playwright test --project=visual", "test:e2e:accessibility": "playwright test --project=accessibility", "test:e2e:chrome": "playwright test --project=chromium", "test:e2e:firefox": "playwright test --project=firefox", "test:e2e:safari": "playwright test --project=webkit", "test:e2e:smoke": "playwright test tests/e2e/auth/login.spec.js tests/e2e/dashboard/user-dashboard.spec.js", "test:e2e:regression": "playwright test --project=chromium --project=firefox", "test:servers": "node scripts/start-test-servers.js"}, "dependencies": {"@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@reduxjs/toolkit": "^2.7.0", "axios": "^1.9.0", "bootstrap": "^5.3.5", "bootstrap-icons": "^1.12.1", "chart.js": "^4.4.9", "file-saver": "^2.0.5", "html5-qrcode": "^2.3.8", "jsbarcode": "^3.11.6", "jspdf": "^2.5.2", "jspdf-autotable": "^3.8.4", "prop-types": "^15.8.1", "qrcode.react": "^4.2.0", "react": "^19.0.0", "react-bootstrap": "^2.10.9", "react-bootstrap-icons": "^1.11.6", "react-chartjs-2": "^5.3.0", "react-datepicker": "^8.3.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-router-dom": "^7.5.3", "react-spinners": "^0.17.0", "recharts": "^2.15.3", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.22.0", "@playwright/test": "^1.48.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "jsdom": "^26.1.0", "redux-mock-store": "^1.5.5", "vite": "^6.3.1", "vitest": "^3.2.3"}}